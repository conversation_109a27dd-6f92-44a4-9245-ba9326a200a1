<?php

namespace App\Modules\CollectDebt\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class CollectDebtRequestResourceCollection extends ResourceCollection
{
	public function toArray($request)
	{
		return [
			'data' => $this->collection->toArray(),
			'meta' => [
				'current_page' => $this->currentPage(),
				'from'         => $this->firstItem(),
				'last_page'    => $this->lastPage(),
				'path'         => "", // $this->path()
				'per_page'     => $this->perPage(),
				'to'           => $this->lastItem(),
				'total'        => $this->total(),
			]
		];
	}

	public function toResponse($request)
	{
		return response()->json($this->toArray($request));
	}
}
