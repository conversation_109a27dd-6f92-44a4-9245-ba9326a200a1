<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\CollectDebtRequestResourceCollection;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestUpdateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestGetByIdRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestReCancelRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestSetStatusRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestSetStatusPaymentRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestSetStatusRecoredRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction\ThucHienCaiTienGhiSoAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\DebtRecoveryRequestCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestUpdateAction\DebtRecoveryRequestUpdateAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetByIdAction\DebtRecoveryRequestGetByIdAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecoredAction\DebtRecoveryRequestRecoredAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestReCancelAction\DebtRecoveryRequestReCancelAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetByIdAction\DebtRecoveryRequestGetDebtLogAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusAction\DebtRecoveryRequestSetStatusAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetByIdAction\SubAction\MappingNguoiThaoTacSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSearchDataAction\DebtRecoveryRequestSearchDataAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\DebtRecoveryRequestSendPaymentAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\DebtRecoveryRequestCheckPaymentAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusPaymentAction\DebtRecoveryRequestSetStatusPaymentAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSetStatusRecoredAction\DebtRecoveryRequestSetStatusRecoredAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestOpenAndMinusFreezeAction\DebtRecoveryRequestOpenAndMinusFreezeAction;
use App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideStatisticManualAction\SubAction\KiemTraLenhTrichNgayVaRaCanhBaoSA;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\CheckRequestPaymentBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestGetListPlanProcessingAction\DebtRecoveryRequestGetListPlanProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequestAction\DebtRecoveryRequestActionGetByRequestIdAction\DebtRecoveryRequestActionGetByRequestIdAction;

class CollectDebtRequestController extends Controller
{
	public function create(DebtRecoveryRequestCreateRequest $request)
	{
		try {
			$collectDebtRequest = app(DebtRecoveryRequestCreateAction::class)->run($request);
			return $this->successResponse($collectDebtRequest->toArray(), $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function update(DebtRecoveryRequestUpdateRequest $request)
	{
		try {
			$collectDebtRequest = app(DebtRecoveryRequestUpdateAction::class)->run($request);
			return $this->successResponse($collectDebtRequest->toArray(), $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function searchData(Request $request)
	{
		try {
			$collectDebtRequestPaginate = app(DebtRecoveryRequestSearchDataAction::class)->run($request);
			$collectDebtRequestResource = new CollectDebtRequestResourceCollection($collectDebtRequestPaginate);
			$response = $collectDebtRequestResource->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function setStatus(DebtRecoveryRequestSetStatusRequest $request)
	{
		DB::beginTransaction();
		try {
			$collectDebtRequest = app(DebtRecoveryRequestSetStatusAction::class)->run($request);
			DB::commit();
			return $this->successResponse($collectDebtRequest->toArray(), $request, 200, $collectDebtRequest->__apiMessage);
		} catch (\Throwable $th) {
			DB::rollBack();
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function setStatusPayment(DebtRecoveryRequestSetStatusPaymentRequest $request)
	{
		try {
			$collectDebtRequest = app(DebtRecoveryRequestSetStatusPaymentAction::class)->run($request);
			return $this->successResponse($collectDebtRequest->toArray(), $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function setStatusRecored(DebtRecoveryRequestSetStatusRecoredRequest $request)
	{
		try {
			$collectDebtRequest = app(DebtRecoveryRequestSetStatusRecoredAction::class)->run($request);
			return $this->successResponse($collectDebtRequest->toArray(), $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getById(DebtRecoveryRequestGetByIdRequest $request)
	{
		try {
			$requestDebtLog = [];
      $actionDebtLog = [];

			$collectDebtRequest = app(DebtRecoveryRequestGetByIdAction::class)->run(
				$request->json('data.id'),
				$request->json('data.fields', ['*'])
			);

      $actionDebtLog = app(DebtRecoveryRequestActionGetByRequestIdAction::class)->run(
				$request->json('data.id')
			);

			if ($request->json('data.with_debt_log')) {
				$requestDebtLog = app(DebtRecoveryRequestGetDebtLogAction::class)->run($collectDebtRequest);
			}

			$warningMessage = ''; // Hien thi tren form duyet yc trich tay, duyet de xuat giam phi
			$isNeedWarning = !empty($request->json('data.is_need_warning', ''));
			
			if ($isNeedWarning && $collectDebtRequest->isTrichTayGiamPhi()) {
				$warningMessage = app(KiemTraLenhTrichNgayVaRaCanhBaoSA::class)->run($collectDebtRequest->contract_code);
			}

			$collectDebtRequest = app(MappingNguoiThaoTacSubAction::class)->run($collectDebtRequest);

			return $this->successResponse([
        'action_log' => $actionDebtLog,
				'request' => $collectDebtRequest->toArray(),
				'request_debt_log' => $requestDebtLog,
				'warning_message' => $warningMessage
			], $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getListPlanProcessing(Request $request)
	{
		$listPlanProcessingIds = app(DebtRecoveryRequestGetListPlanProcessingAction::class)->run();
		return $this->successResponse([
			'plan_ids_array' => $listPlanProcessingIds
		], $request);
	}

	public function sendPayment(Request $request)
	{
		try {
			$results = app(DebtRecoveryRequestSendPaymentAction::class)->init();
			return $this->successResponse($results, $request);
		}catch(\Exception $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	
	public function checkPayment(Request $request)
	{
		try {
			$result = app(DebtRecoveryRequestCheckPaymentAction::class)->run($request);
			
			return $result;
		}catch(\Exception $th) {
			
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
	
	public function recored(Request $request)
	{
		try {
			$recoredRequestsIds = app(ThucHienCaiTienGhiSoAction::class)->initGhiSo($request);
			return $this->successResponse($recoredRequestsIds, $request);
		}catch(\Exception $th) {
			TelegramAlert::sendGhiSo(traceErr($th));
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoveryRequestOpenAndMinusFreeze(Request $request)
	{
		try {
			$collectDebtRequest = app(DebtRecoveryRequestOpenAndMinusFreezeAction::class)->run($request);
			return $this->successResponse(['collectdebt_request_id' => $collectDebtRequest->id], $request);
		}catch(\Exception $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function DebtRecoveryRequestReCancel(DebtRecoveryRequestReCancelRequest $request)
	{
		try {
			$collectDebtRequest = app(DebtRecoveryRequestReCancelAction::class)->run($request);
			$msg = sprintf(
				'Tái hủy lệnh: %s (chứng từ: %s) thành công', 
				$collectDebtRequest->partner_request_id,
				$collectDebtRequest->partner_transaction_id
			);
			return $this->successResponse(['collectdebt_request_id' => $collectDebtRequest->id], $request, 200, $msg);
		}catch(\Exception $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

} // End class
