<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction;

use Exception;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction\SubAction\TaoBanGhiLedgerSubAction;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Model\CollectDebtStatisticPartner;

class LedgerRecordBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtRequest::query()
			->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
			->where(function ($q) {
				$q->orWhere(function ($query) {
					// Trích MPOS cho cae 2 luồng
					$query->where('payment_method_code', 'MPOS')
						->whereIn('status', [CollectDebtEnum::REQUEST_STT_DA_DUYET, CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA])
						->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA);
				})
					->orWhere(function ($query) {
						// Tự động
						$query->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
							->where('status', CollectDebtEnum::REQUEST_STT_DA_DUYET)
							->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA);
					})
					->orWhere(function ($query) {
						// Tất toán giảm phí
						$query->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TAY)
							->whereHas('collectDebtRequestActions', function ($q) {
								$q->where('action_code', 'APPROVE2')
									->where('type', CollectDebtEnum::REQUEST_ACTION_TYPE_GIAM_PHI);
							});
					});
			})->chunkById(self::CHUNK_LIMIT, function (Collection $collectDebtRequests) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processLedgerRecordBatch($client, $collectDebtRequests);
			});

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}


	private function processLedgerRecordBatch(Client $client, Collection $collectDebtRequests): void
	{
		// Generator
		$requests = function () use ($collectDebtRequests) {
			foreach ($collectDebtRequests as $rq) {
				$url = config('app.url') . '/HandleLedgerRecord/' . $rq->partner_request_id;
				yield $rq->partner_request_id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $partnerRequestId) {
				// $body = (string)$response->getBody();
				Log::info("[LedgerRecord --->$partnerRequestId] success");
				$this->__processedIds[] = $partnerRequestId;
			},
			'rejected' => function (\Throwable $reason, string $partnerRequestId) {
				$msg = "[LedgerRecord --->$partnerRequestId] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function HandleLedgerRecord(string $partnerRequestId)
	{
		$collectDebtRequest = CollectDebtRequest::query()->firstWhere(['partner_request_id' => $partnerRequestId]);

		if (!$collectDebtRequest) {
			return 'Request NotFound';
		}

		if ($collectDebtRequest->status_recored != CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO) {
			return 'Request recored processing...';
		}

		$updateLenDangGhiSo = CollectDebtRequest::query()
			->where('id', $collectDebtRequest->id)
			->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
			->update([
				'status_recored' => CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO
			]);

		if (!$updateLenDangGhiSo) {
			throw new Exception('loi khong the update len trang thai DANG GHI SO');
		}

		$collectDebtRequest = CollectDebtRequest::find($collectDebtRequest->id); // khong dung refresh

		if ($collectDebtRequest->status_recored != CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO) {
			throw new Exception('ban ghi yeu cau dang khong o trang thai DANG GHI SO');
		}

		DB::beginTransaction();
		try {
			// cho ghi so vi khong co partner
			if ($collectDebtRequest->isYeuCauTrichLichThuQuaKhu()) {
				$yeuCauGhiSoThanhCong = $this->__thucHienGhiSo($collectDebtRequest);
				DB::commit();
				return $yeuCauGhiSoThanhCong;
			}

			// cho ghi so vi khong co partner
			if ($collectDebtRequest->isTrichTayGiamPhi()) {
				$yeuCauGhiSoThanhCong = $this->__thucHienGhiSo($collectDebtRequest);
				DB::commit();
				return $yeuCauGhiSoThanhCong;
			}


			$collectDebtPartner = CollectDebtPartner::query()
				->where('payment_method_code', $collectDebtRequest->payment_method_code)
				->where('partner_request_id', $collectDebtRequest->partner_request_id)
				->first();

			if (!$collectDebtPartner) {
				throw new Exception('khong co thong tin partner cua yc');
			}


			// Kiem tra so tien tren partner va so tien thanh cong tren yeu cau
			if ($collectDebtPartner->amount_receiver != $collectDebtRequest->amount_receiver) {


				if ($collectDebtPartner->payment_method_code == 'MPOS') {


					// so tien trich thuc te
					$soTienTrichTc = $collectDebtPartner->amount_receiver;

					$collectDebtRequest->amount_receiver = $soTienTrichTc;
					$collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO;
					$result = $collectDebtRequest->save();

					if (!$result) {

						throw new Exception('So tien thanh cong tren yc bi khac voi partner');
					}

					DB::commit();
					return $collectDebtRequest;
				}

				throw new Exception('phat hien so tien cua partner khac voi so tien cua yeu cau');
			}

			$yeuCauGhiSoThanhCong = $this->__thucHienGhiSo($collectDebtRequest);
			DB::commit();

			return $yeuCauGhiSoThanhCong;
		} catch (\Throwable $th) {

			DB::rollBack();

			$updateVeTrangThaiChuaGhiSo = CollectDebtRequest::query()
				->where('id', $collectDebtRequest->id)
				->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO)
				->update([
					'status_recored' => CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO
				]);
			if (!$updateVeTrangThaiChuaGhiSo) {
			}

			throw $th;
		}
	}

	private function __thucHienGhiSo(CollectDebtRequest $collectDebtRequest)
	{
		$collectDebtLedger = app(TaoBanGhiLedgerSubAction::class)->run($collectDebtRequest);

		if (!$collectDebtLedger) {
			throw new Exception('khong the tao ban ghi ghi so.');
		}


		$updateSoVeTrangThaiDaGhiSo = CollectDebtRequest::query()
			->where('id', $collectDebtRequest->id)
			->update([
				'status_recored' => CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO,
				'time_recored' => now()->timestamp,
				'recored_by' => Helper::getCronJobUser(),

				'time_completed' => time(),
				'completed_by'  => Helper::getCronJobUser(),
			]);

		if (!$updateSoVeTrangThaiDaGhiSo) {
			throw new Exception('[THAT BAI] - khong the cap nhat trang thai la: DA GHI SO');
		}

		CollectDebtProcessing::query()->where([
			'contract_code' => $collectDebtRequest->contract_code,
			'partner_request_id' => $collectDebtRequest->partner_request_id
		])->delete();

		CollectDebtStatisticPartner::query()->where([
			'contract_code' => $collectDebtRequest->contract_code,
		])->update(['status_sync' => 0, 'time_sync' => now()->timestamp]);
		
		return $collectDebtRequest;
	}
}  // End class