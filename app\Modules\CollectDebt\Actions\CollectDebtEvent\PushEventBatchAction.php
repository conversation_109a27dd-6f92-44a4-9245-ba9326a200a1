<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent;

use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\DebtRecoveryContractEventCreateSendAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\DebtRecoveryContractEventBuildContentImproveAction;

class PushEventBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtContractEvent::query()
			->whereIn('status', [CollectDebtEnum::EVENT_STT_MOI_TAO, CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL])
			->where('service_care_code', 'MAIL')
			->select(['id', 'contract_code', 'status'])
			->chunkById(self::CHUNK_LIMIT, function (Collection $collectDebtRequests) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processBatch($client, $collectDebtRequests);
			});

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}

	private function processBatch(Client $client, Collection $listEvent): void
	{
		// Generator
		$requests = function () use ($listEvent) {
			foreach ($listEvent as $ev) {
				$url = config('app.url') . '/HandleEvent/' . $ev->id;
				yield $ev->id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, $id) {
				// $body = (string)$response->getBody();
				$this->__processedIds[] = $id;
			},
			'rejected' => function (\Throwable $reason, $id) {
				$msg = "[SendMailEvent --->$id] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function HandleEvent($id)
	{
		$collectDebtEvent = CollectDebtContractEvent::query()->find($id);
		
		if ($collectDebtEvent->status > CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL) {
			return true;
		}

		if ($collectDebtEvent->status == CollectDebtEnum::EVENT_STT_MOI_TAO) {
			$content =  app(DebtRecoveryContractEventBuildContentImproveAction::class)->__buildMailContent($collectDebtEvent);
			$collectDebtEvent->content = $content;
			$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
			$collectDebtEvent->save();
		}

		// send mail
		return app(DebtRecoveryContractEventCreateSendAction::class)->__runMail($collectDebtEvent);
	}
}  // End class