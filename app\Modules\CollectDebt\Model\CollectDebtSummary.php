<?php

namespace App\Modules\CollectDebt\Model;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtDigitalTmp;

class CollectDebtSummary extends Model
{
	protected $table = 'debt_recovery_summary';
	public $timestamps = false;
	protected $guarded = [];

	public function collectDebtProcessing() {
		return $this->hasOne(CollectDebtProcessing::class, 'contract_code', 'contract_code');
	}

	public function collectDebtDigitalQuaHanTmp() {
		return $this->hasOne(CollectDebtDigitalTmp::class, 'contract_code', 'contract_code')->where('type', 'over_due');
	}

	public function collectDebtShare()
	{
		return $this->hasOne(CollectDebtShare::class, 'contract_code', 'contract_code');
	}

	public function collectDebtRequests()
	{
		return $this->hasMany(CollectDebtRequest::class, 'contract_code', 'contract_code');
	}

	public function collectDebtSchedules() {
		return $this->hasMany(CollectDebtSchedule::class, 'contract_code', 'contract_code');
	}

	public static function listStatus(): array
	{
		return [
			CollectDebtEnum::SUMMARY_STT_DANG_CAP_NHAP => 'Đang cập nhập',
			CollectDebtEnum::SUMMARY_STT_DANG_THUC_HIEN => 'Đang thực hiện kết toán',
			CollectDebtEnum::SUMMARY_STT_DA_HOAN_THANH => 'Đã hoàn thành',
			CollectDebtEnum::SUMMARY_STT_DA_TU_CHOI => 'Đã từ chối',
		];
	}

	public function putJson(array $data, string $field = 'other_data')
	{
		$fields = json_decode($this->$field, true);
		$fields[] = $data;
		return json_encode($fields, JSON_UNESCAPED_UNICODE);
	}

	public function getSummaryOtherData(): array
	{
		return json_decode($this->other_data, true);
	}

	public function getSummaryOtherDataItem(string $type = 'PLAN')
	{
		$otherData = $this->getSummaryOtherData();
		return collect($otherData)->where('type', $type)->first();
	}

	public function getSummaryOtherDataIndex(string $type = 'PLAN')
	{
		$otherData = $this->getSummaryOtherData();
		return collect($otherData)->search(function ($item) use ($type) {
			return $item['type'] == $type;
		});
	}

	public function isHopDongDaTatToan(): bool
	{
		return $this->status_contract == CollectDebtEnum::SUMMARY_STATUS_CONTRACT_DA_TAT_TOAN;
	}

	public function isDaQuaHanHopDong(): bool {
		$contractData = $this->getContractData();
		return time() > $contractData['time_end'];
	}

	public function getContractData(): array
	{
		return json_decode($this->contract_data, true);
	}

	public function getProfileId() {
		$contractData = $this->getContractData();
		return $contractData['profile_id'];
	}

	public function getCurrency(): string
	{
		$contractData = $this->getContractData();
		return $contractData['currency'];
	}

	public function getSoTienGiaiNgan(): float
	{
		$contractData = $this->getContractData();
		return $contractData['contract_receiver'] ?? 0;
	}

	public function isHopDongSummaryTrichKy(): bool
	{
		$contractData = json_decode($this->contract_data, true);
		return $contractData['type'] == 3; // 1: Trích ngày | 3: Trích kỳ
	}

	public function isHopDongSummaryTrichNgay(): bool
	{
		$contractData = json_decode($this->contract_data, true);
		return $contractData['type'] == 1; // 1: Trích ngày | 3: Trích kỳ
	}

	public function getSoNgayBiQuaHan(): int
	{
		$contractData = $this->getContractData();
		$ngayKetThucHopDong = Carbon::parse($contractData['time_end_as_date']);

		if (now()->gte($ngayKetThucHopDong)) {
			return now()->diffInDays($ngayKetThucHopDong);
		}
		return 0;
	}

	public function isHopDongTest(): bool
	{
		return !Str::startsWith($this->contract_code, 'MPOS-');
	}

	public function collectDebtLedger()
	{
		return $this->hasMany(CollectDebtLedger::class, 'contract_code', 'contract_code');
	}

	public function getTimeEndAsDate() {
		$contractData = $this->getContractData();
		$ngayKetThucHopDong = Carbon::parse($contractData['time_end_as_date']);
		return $ngayKetThucHopDong;
	}

	public function getTimeStartAsDate() {
		$contractData = $this->getContractData();
		$ngayBatDauHopDong = Carbon::parse($contractData['time_start_as_date']);
		return $ngayBatDauHopDong;
	}

	public function getTongTienTrichThanhCong() {
		return $this->total_amount_paid + $this->total_fee_paid;
	}

	public function isTongTienTrichThanhCongLonHonGiaTriHopDong() {
		return $this->getTongTienTrichThanhCong() >= $this->contract_amount;
	}

	// Thong tin ve phi
	public function getTongPhiDaHoan() {
		return $this->fee_overdue_cycle_refund + $this->fee_overdue_refund;
	}

	public function getTongPhiDaGiam() {
		return $this->fee_overdue_cycle_reduction + $this->fee_overdue_reduction;
	}

	public function getTongPhiDaThu() {
		return $this->fee_overdue_cycle_paid + $this->fee_overdue_paid;
	}

	public function getTongPhiDuocSinhRa() {
		return $this->fee_overdue_cycle + $this->fee_overdue;
	}

	public function getTongPhiCoTheHoan() {
		$phiCoTheHoan = $this->getTongPhiDaThu() - $this->getTongPhiDaHoan() - $this->getTongPhiDaGiam();
		
		mylog([
			'ContractCode' => $this->contract_code,
			'PhiCoTheHoan' => $phiCoTheHoan
		]);

		return $phiCoTheHoan;
	}

	public function getOnlyPhiChamKyCoTheHoan() {
		return $this->fee_overdue_cycle_paid - $this->fee_overdue_cycle_reduction
																				 - $this->fee_overdue_cycle_refund;
	}

	public function getOnlyPhiQuaHanCoTheHoan() {
		return $this->fee_overdue_paid - $this->fee_overdue_reduction
																	 - $this->fee_overdue_refund;
	}

	public function getTongTienConPhaiTra() {
		return ($this->contract_amount + $this->getTongPhiDuocSinhRa())
			- ($this->getTongTienTrichThanhCong() + $this->getTongPhiDaGiam());
	}

	public function isDenHanHopDong() {
		return now()->isSameDay($this->getTimeEndAsDate());
	}

	public function getDuNoGocPhaiTra() {
		return $this->contract_amount - $this->total_amount_paid;
	}

	public function getPhiConPhaiTra() {
		return $this->getTongPhiDuocSinhRa() - $this->getTongPhiDaThu() - $this->getTongPhiDaGiam();
	}
} // End class
