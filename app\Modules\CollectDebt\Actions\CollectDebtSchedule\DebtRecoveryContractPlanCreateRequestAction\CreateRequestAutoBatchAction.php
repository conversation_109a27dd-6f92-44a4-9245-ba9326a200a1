<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction;

use Exception;
use Carbon\Carbon;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use App\Lib\NextlendCore;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtConfigAuto;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Model\Traits\CollectDebtSchedule\PlanSortableCollectionByRule;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\BuildParamTaoYeuCauTuDongTask;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction\TaoYeuCauTuDongMposTuLichThuSubAction\Task\ThucHienTaoLenhTrichTuDongTask;

class CreateRequestAutoBatchAction
{
	private const CHUNK_LIMIT = 30;
	private const POOL_CONCURRENCY = 5;
	private const HTTP_TIMEOUT = 10;
	private const BATCH_LIMIT_IN_SECONDS = 60; // quá trình chunks rồi batch chỉ trong 60s mà thôi

	private array $__processedIds = [];
	private array $__errorIds = [];


	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtSummary::query()
			->whereDoesntHave('collectDebtProcessing')
			->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN)
			->chunkById(self::CHUNK_LIMIT, function (Collection $listSummary) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processBatchCreateRequest($client, $listSummary);
			});

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}

	private function createHttpClient(): Client
	{
		return new Client([
			'base_uri' => config('app.url'),
			'timeout' => self::HTTP_TIMEOUT,
			'verify' => false
		]);
	}

	private function processBatchCreateRequest(Client $client, Collection $collectDebtSummaries): void
	{
		// Generator
		$requests = function () use ($collectDebtSummaries) {
			foreach ($collectDebtSummaries as $summary) {
				$url = config('app.url') . '/HandleCreateRequestAuto/' . $summary->contract_code;
				yield $summary->contract_code => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $contractCode) {
				// $body = (string)$response->getBody();
				Log::info("[TaoLenhTrichTuDong --->$contractCode] success");
				$this->__processedIds[] = $contractCode;
			},
			'rejected' => function (\Throwable $reason, string $contractCode) {
				$msg = "[TaoLenhTrichTuDong --->$contractCode] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function handleCreateRequestAuto(string $contractCode)
	{
		$listContractExclude = $this->getListHopDongCanNe();

		// 1. Đang bị exclude thì không cho tạo lệnh
		if (in_array($contractCode, $listContractExclude)) {
			return $contractCode;
		}

		// 2. Chỉ lấy ra các lịch có `is_process` là CHƯA XỬ LÝ
		$plan = CollectDebtSchedule::query()
			->where('contract_code', $contractCode)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->where('rundate', '<=', now()->format('Ymd'))
			->orderByRaw('rundate ASC, cycle_number ASC, type ASC, is_settlement ASC')
			->first();

		if (!$plan) {
			// Không có plan thì kết thúc và giải phóng luồng luôn
			return $contractCode; 
		}

		// 2. Update lịch vừa select lên thành đang xử lý ---> để thực hiện xử lý
		$wasUpdateProcessing = CollectDebtSchedule::query()
			->where('contract_code', $plan->contract_code)
			->where('id', $plan->id)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->update([
				'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
			]);

		if (!$wasUpdateProcessing) {
			$msg = "[ErrorUpdateProcessing---->$contractCode]" . 'Loi khong the update len thanh dang xu ly';
			Log::info($msg, ['planId' => $plan->id]);
			throw new Exception($msg);
		}


		// 3. 
		// Cập nhật các bản ghi lịch thu có cùng rundate về trạng thái ĐANG XỬ LÝ
		// Không thể biết có lịch quá khứ hay không, nên chỉ bắt catch
		try {
			$updatedPlanRows = CollectDebtSchedule::query()
				->where('contract_code', $plan->contract_code)
				->where('rundate', $plan->rundate)
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
				->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
				->where('id', '!=', $plan->id)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY
				]);
		} catch (\Throwable $th) {
			Log::info("[ErrorUpdateProcessing---->$contractCode]" . 'Loi update cac lich thu cung rundate', ['details' => Helper::traceError($th)]);

			// Doan nay phai update plan hien tai =>  ve da chua xu ly
			$updatedVeChuaXuLy = CollectDebtSchedule::query()->where('id', $plan->id)
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
				]);

			throw $th;
		}


		$listLichThuRefresh = CollectDebtSchedule::query()
																						 ->join('debt_recovery_share', 'debt_recovery_share.contract_code', '=', 'debt_recovery_contract_plan.contract_code')
																						 ->where('debt_recovery_contract_plan.contract_code', $plan->contract_code)
																						 ->where('debt_recovery_contract_plan.rundate', $plan->rundate)
																						 ->where('debt_recovery_contract_plan.is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
																						 ->where('debt_recovery_contract_plan.status', CollectDebtEnum::SCHEDULE_STT_MOI)
																						 ->select([
																								'debt_recovery_contract_plan.*',

																								// Share
																								'debt_recovery_share.partner_code', 
																								'debt_recovery_share.payment_guide'
																							])
																						 ->get();


		$isToanBoLichLaDangXuLy = $listLichThuRefresh->every(function (CollectDebtSchedule $p) {
			return $p->is_process == CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY;
		});

		// Toàn bộ các lịch phải là ĐANG XỬ LÝ --> thì mới có thể tạo yêu cầu được
		throw_if(!$isToanBoLichLaDangXuLy, new Exception('Toan bo lich chua ve dang xu ly'));

		$listLichThuTaoYeuCau = $listLichThuRefresh;

		$listLichThuTaoYeuCau = app(PlanSortableCollectionByRule::class)->sortCollection($listLichThuTaoYeuCau);

		// 5. Start transaction
		DB::beginTransaction();
		try {
			$buildParamTaoYeuCau = app(BuildParamTaoYeuCauTuDongTask::class)->run($listLichThuTaoYeuCau);
			$collectDebtRequest = app(ThucHienTaoLenhTrichTuDongTask::class)->run($buildParamTaoYeuCau, $listLichThuTaoYeuCau);

			$collectDebtProcessing = CollectDebtProcessing::query()->forceCreate([
				'contract_code' => $collectDebtRequest->contract_code,
				'partner_request_id' => $collectDebtRequest->partner_request_id,
				'expired_at' => Carbon::createFromTimestamp($collectDebtRequest->time_expired),
				'created_at' => now(),
				'updated_at' => now()
			]);

			if (!$collectDebtProcessing) {
				throw new Exception('Loi khong tao duoc ban ghi processing...');
			}
			
			DB::commit();


			return $collectDebtRequest;
		} catch (\Throwable $th) {
			DB::rollBack();
			Log::info("[ErrorTaoYeuCau----> $contractCode]", ['details' => Helper::traceError($th)]);

			// Update toàn bộ lịch thu cần tạo yc về is_process: CHƯA XỬ LÝ
			$updated = CollectDebtSchedule::query()
				->where('contract_code', $plan->contract_code)
				->whereIn('id', $listLichThuTaoYeuCau->pluck('id')->toArray())
				->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_DANG_XU_LY)
				->update([
					'is_process' => CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY
				]);

			if (!$updated) {
				throw new Exception('Loi rollback trang thai, can sua tay');
			}
		}
	}

	public function getListHopDongCanNe(): array
	{
		$listHopDongDungJob = CollectDebtConfigAuto::getHopDongDungJobCache();
		return $listHopDongDungJob;
	}
} // End class