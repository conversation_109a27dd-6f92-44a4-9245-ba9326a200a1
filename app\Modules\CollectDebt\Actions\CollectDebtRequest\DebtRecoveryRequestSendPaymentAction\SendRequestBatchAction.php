<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction;

use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;

class SendRequestBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtRequest::query()
			->where('payment_method_code', 'MPOS')
			->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
			->where('is_payment', CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT)
			->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI)
			->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
			->where(function ($q) {
				$q->whereNull('time_sended')
					->orWhereRaw(sprintf('time_sended + %d < %d', config('nextlend.THOI_GIAN_XU_LY_LENH_LOI_RO_RANG', 20) * 60, time()));
			})
			->where('time_expired', '>', time())
			->chunkById(self::CHUNK_LIMIT, function (Collection $collectDebtRequests) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processBatch($client, $collectDebtRequests);
			});

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}

	private function processBatch(Client $client, Collection $collectDebtRequests): void
	{
		// Generator
		$requests = function () use ($collectDebtRequests) {
			foreach ($collectDebtRequests as $rq) {
				$url = config('app.url') . '/HandleSendRequestMpos/' . $rq->partner_request_id;
				yield $rq->partner_request_id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $partnerRequestId) {
				// $body = (string)$response->getBody();
				Log::info("[SendRequest --->$partnerRequestId] success");
				$this->__processedIds[] = $partnerRequestId;
			},
			'rejected' => function (\Throwable $reason, string $partnerRequestId) {
				$msg = "[SendRequest --->$partnerRequestId] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function HandleSendRequestMpos(string $partnerRequestId)
	{
		$collectDebtRequest = CollectDebtRequest::query()
			->with('collectDebtShareOnly:contract_code,priority,partner_code')
			->where('partner_request_id', $partnerRequestId)
			->where('payment_method_code', 'MPOS')
			->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
			->where('is_payment', CollectDebtEnum::REQUEST_IS_PAYMENT_CO_GUI_DOI_TAC_TT)
			->where('status_payment', CollectDebtEnum::REQUEST_STT_PM_CHUA_GUI)
			->where('create_from', CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG)
			->where('time_expired', '>', time())
			->first();

		if (!$collectDebtRequest) {
			return 'Request NotFound';
		}

		if ($collectDebtRequest->isUnsentPayment()) {
			$now = now();

			if ($collectDebtRequest->collectDebtShareOnly->priority == 1 && $now->lt(today()->addMinutes(30))) {
				return $partnerRequestId;
			}

			$rq = app(SendRequestViaMposSubAction::class)->run($collectDebtRequest);
		}

		return $partnerRequestId;
	}
}  // End class