<?php

use Illuminate\Support\Facades\Route; 
use App\Modules\CollectDebt\Actions\CollectDebtEvent\PushEventBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\CreateOverCycleOverdueEventAction;

/*-------------------Send event dạng batch--------------------*/
	Route::any('/PushEventBatchAction', function () {
		return app(PushEventBatchAction::class)->run();
	});

	Route::any('/HandleEvent/{id}', function ($id) {
		return app(PushEventBatchAction::class)->HandleEvent($id);
	});
/*-------------------./ End Send event dạng batch--------------------*/

/*-------------------Event qu--------------------*/
	Route::any('/CreateOverCycleOverdueEventAction', function () {
		return app(CreateOverCycleOverdueEventAction::class)->run();
	});

	Route::any('/HandleOverCycleOverdue/{id}', function ($id) {
		return app(CreateOverCycleOverdueEventAction::class)->HandleOverCycleOverdue($id);
	});
/*-------------------./ End Send event dạng batch--------------------*/


