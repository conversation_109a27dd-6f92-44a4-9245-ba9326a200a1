<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryMailOverDueAction;

use DB;
use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtShare\DebtRecoveryShareGetContractCodeAction\DebtRecoveryShareGetContractCodeAction;

class DebtRecoverySummaryMailOverDueAction
{
	private array $__summaryExceptIds = [];

	public function initGuiMailQuaHan($request)
	{
		mylog(['init tao event mail QUA HAN' => 'ok']);

		$returnData = [];

		$mode = $request->get('mode', 'ALL');
		switch ($mode) {
			case 'CHAN': 
				$whereRaw = 'id % 2 = 0';
				break;

			case 'LE': 
				$whereRaw = 'id % 2 != 0';
				break;

			default: 
				$whereRaw = '1';
				break;
		}

		for ($i = 0; $i <= 30; $i++) {
			mylog(['--------------------------------------' => sprintf('%s ------------------------------', $i)]);

			try {
				$result = $this->run($whereRaw);
				if ($result == 'EMPTY') {
					$returnData[] = 'khong co thong tin HD QUA HAN can gui mail';
					break;
				}

				if ($request && optional($result)->id) {
					$returnData[] = $result->only(['id', 'contract_code']);
				}
			} catch (\Throwable $th) {
				mylog(['[LOI XY LY]' => Helper::traceError($th)]);
				@TelegramAlert::sendEmails(Helper::traceError($th));
				continue;
			} finally {
				usleep(300000);
			}
		}

		return $returnData;
	}

	public function run($whereRaw)
	{
		$collectDebtSummary = CollectDebtSummary::query()
																						->whereRaw($whereRaw)
																						->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN)
																						->where('is_send_mail_overdue', CollectDebtEnum::SUMMARY_CO_GUI_MAIL_QUA_HAN);
		if (!empty($this->__summaryExceptIds)) {
			$collectDebtSummary = $collectDebtSummary->whereNotIn('id', $this->__summaryExceptIds);
		}
		$collectDebtSummary = $collectDebtSummary->first();
		
		if (!$collectDebtSummary) {
			mylog(['[LOI]' => 'khong co thong tin HD QUA HAN can gui mail']);
			return 'EMPTY';
		}

		$updateLenDangXuLy = CollectDebtSummary::query()
			->where('id', $collectDebtSummary->id)
			->where('is_send_mail_overdue', CollectDebtEnum::SUMMARY_CO_GUI_MAIL_QUA_HAN)
			->update([
				'is_send_mail_overdue' => CollectDebtEnum::SUMMARY_DANG_GUI_MAIL_QUA_HAN
			]);

		if (!$updateLenDangXuLy) {
			mylog(['[LOI]' => 'Khong the update trang thai xu ly mail QUA HAN thanh dang xu ly']);
			throw new Exception('Khong the update trang thai xu ly mail QUA HAN thanh dang xu ly');
		}

		$days = $collectDebtSummary->number_day_overdue;
		$code = '';

		if ($days >= 0 && $days < 5) {
			$code = 'NOTIFY_CONTRACT_DUE1';
		} else if ($days >= 5 && $days <= 10) {
			$code = 'NOTIFY_CONTRACT_DUE2';
		} else if ($days >= 11) {
			$code = 'NOTIFY_CONTRACT_DUE3';
		}

		mylog([
			'Ban ghi summary' => $collectDebtSummary,
			'So ngay qua han' => $days,
			'Ma Email su dung' => $code,
		]);

		throw_if(empty($code), new Exception('Khong biet la QUA HAN LOAI GI'));
		$this->__summaryExceptIds[] = $collectDebtSummary->id;

		DB::beginTransaction();
		try {
			$createdEvent = $this->__createEvent($code, $collectDebtSummary);

			if (!$createdEvent) {
				mylog(['[LOI]' => 'Co loi xay ra khi tao event']);
				throw new Exception('Co loi xay ra khi tao event');
			}

			// tao duoc event thi cap nhat summary ve chua xu ly
			$updateSummaryVeKhongCanGuiMail = CollectDebtSummary::query()
																													->where('id', $collectDebtSummary->id)
																													->update([
																														'is_send_mail_overdue' => CollectDebtEnum::SUMMARY_KHONG_GUI_MAIL_QUA_HAN
																													]);
			if (!$updateSummaryVeKhongCanGuiMail) {
				mylog(['[LOI]' => 'Khong the cap nhat summary ve trang thai ko phai gui email qua han nua']);
			}

			DB::commit();
			return $collectDebtSummary;
		}catch(\Throwable $th) {
			mylog(['[LOI TRANSACTION]' => Helper::traceError($th)]);
			DB::rollBack();

			$updatedVePhaiXuLy = CollectDebtSummary::query()
				->where(['id' => $collectDebtSummary->id, 'is_send_mail_overdue' => CollectDebtEnum::SUMMARY_DANG_GUI_MAIL_QUA_HAN])
				->update([
					'is_send_mail_overdue' => CollectDebtEnum::SUMMARY_CO_GUI_MAIL_QUA_HAN
				]);

			if (!$updatedVePhaiXuLy) {
				mylog(['[LOI]' => 'loi cap nhat ve phai xu ly summary']);
			}


			throw $th;
		}
	}

	public function __createEvent($code, $dataSummary)
	{
		if ($dataSummary->contract_code == 'MPOS-2503071524924-L6') {
			return true;
		}

		$dataShare = $this->__getDataShare($dataSummary);

		$rq = new \App\Modules\EmailRemind\Request\CollectDebtContractEvent\DebtRecoveryContractEventCreateRequest();
		$inputs = [
			'category_care_code' => $code,
			'service_care_code' => 'MAIL',
			'data' => $this->__setDataEvent($dataShare),
			'description' => 'Tạo Event',
			'other_data' => [
				'summary' => $dataSummary->toArray(),
			],
			'time_start' => time(),
			'contract_code' => $dataSummary->contract_code,
		];
		mylog(['Thong tin input create event' => $inputs]);

		$rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));
		$create = (new \App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateAction\DebtRecoveryContractEventCreateAction())->run($inputs);

		mylog(['Thong tin create event' => $create]);

		if (isset($create['id']) && $create['id']) {
			return true;
		}
		return false;
	}

	protected function __setDataEvent($value)
	{
		$data = [];

		if ($value) {
			if (isset($value['company_data']) && $value['company_data']) {
				$data['company'] = json_decode($value['company_data'], true);
			}
			if (isset($value['contract_data']) && $value['contract_data']) {
				$data['contract'] = json_decode($value['contract_data'], true);
			}
			if (isset($value['profile_data']) && $value['profile_data']) {
				$data['profile'] = json_decode($value['profile_data'], true);
			}
			if (isset($value['payment_guide']) && $value['payment_guide']) {
				$data['payment'] = json_decode($value['payment_guide'], true);
			}
			if (isset($value['list_fee']) && $value['list_fee']) {
				$data['list_fee'] = json_decode($value['list_fee'], true);
			}
		}
		return $data;
	}

	protected function __getDataShare($dataSummary)
	{
		$inputs = [
			'contract_code' => $dataSummary->contract_code
		];

		$dataShare = app(DebtRecoveryShareGetContractCodeAction::class)->run($inputs);

		mylog(['Thong tin data share' => $dataShare]);

		throw_if(!$dataShare, new Exception("DATA SHARE EMPTY"));

		return $dataShare;
	}
}
