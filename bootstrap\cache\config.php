<?php return array (
  'app' => 
  array (
    'name' => 'RequestDebt',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://nextpay-web.local.com/api-request-debt/public',
    'asset_url' => NULL,
    'timezone' => 'Asia/Ho_Chi_Minh',
    'locale' => 'vi',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'key' => 'base64:Xj1y4pxHlUztVk+zDpsYVj6eUdfdTH/P53WskG/q1bg=',
    'cipher' => 'AES-256-CBC',
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Cookie\\CookieServiceProvider',
      6 => 'Illuminate\\Database\\DatabaseServiceProvider',
      7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      10 => 'Illuminate\\Hashing\\HashServiceProvider',
      11 => 'Illuminate\\Mail\\MailServiceProvider',
      12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      15 => 'Illuminate\\Queue\\QueueServiceProvider',
      16 => 'Illuminate\\Redis\\RedisServiceProvider',
      17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      18 => 'Illuminate\\Session\\SessionServiceProvider',
      19 => 'Illuminate\\Translation\\TranslationServiceProvider',
      20 => 'Illuminate\\Validation\\ValidationServiceProvider',
      21 => 'Illuminate\\View\\ViewServiceProvider',
      22 => 'App\\Providers\\AppServiceProvider',
      23 => 'App\\Providers\\AuthServiceProvider',
      24 => 'App\\Providers\\EventServiceProvider',
      25 => 'App\\Providers\\RouteServiceProvider',
      26 => 'App\\Providers\\RepositoryServiceProvider',
      27 => 'App\\Modules\\ModuleServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Redis' => 'Illuminate\\Support\\Facades\\Redis',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'api' => 
      array (
        'driver' => 'token',
        'provider' => 'users',
        'hash' => false,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_resets',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => '',
        'secret' => '',
        'app_id' => '',
        'options' => 
        array (
          'cluster' => 'mt1',
          'useTLS' => true,
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'redis',
    'stores' => 
    array (
      'apc' => 
      array (
        'driver' => 'redis',
      ),
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'debt_recovery_cache',
        'connection' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
    ),
    'prefix' => 'NL_TRICHNO_V4',
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'lending_debt',
        'prefix' => '',
        'foreign_key_constraints' => true,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '************',
        'port' => '3306',
        'database' => 'lending_debt',
        'username' => 'nextlend',
        'password' => 'k2Mgr0dczP9K,0hnEdRFJ',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => false,
        'engine' => NULL,
        'options' => 
        array (
          12 => true,
          2 => 20,
          1002 => 'SET SESSION wait_timeout=180',
        ),
        'read' => 
        array (
          0 => 
          array (
            'host' => '************',
            'port' => '3306',
            'database' => 'lending_debt',
            'username' => 'nextlend',
            'password' => 'k2Mgr0dczP9K,0hnEdRFJ',
          ),
        ),
        'write' => 
        array (
          0 => 
          array (
            'host' => '************',
            'port' => '3306',
            'database' => 'lending_debt',
            'username' => 'nextlend',
            'password' => 'k2Mgr0dczP9K,0hnEdRFJ',
          ),
        ),
        'sticky' => true,
      ),
      'lending_data' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '************',
        'port' => '3306',
        'database' => 'lending_data',
        'username' => 'nextlend',
        'password' => 'k2Mgr0dczP9K,0hnEdRFJ',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => false,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => '************',
        'port' => '3306',
        'database' => 'lending_debt',
        'username' => 'nextlend',
        'password' => 'k2Mgr0dczP9K,0hnEdRFJ',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'schema' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => '************',
        'port' => '3306',
        'database' => 'lending_debt',
        'username' => 'nextlend',
        'password' => 'k2Mgr0dczP9K,0hnEdRFJ',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 'migrations',
    'redis' => 
    array (
      'client' => 'predis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'requestdebt_database_',
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '***********',
        'password' => NULL,
        'port' => '30010',
        'database' => '1',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '***********',
        'password' => NULL,
        'port' => '30010',
        'database' => '1',
      ),
    ),
  ),
  'debt' => 
  array (
    'mail' => 
    array (
      'NHAC_NO_THANH_TOAN' => 
      array (
        'sender' => 
        array (
          'name' => 'Hỗ trợ vốn',
          'email' => '<EMAIL>',
        ),
        'cc_htv' => 
        array (
          0 => 
          array (
            'name' => 'Hỗ trợ vốn Nextlend',
            'identifier_account' => '<EMAIL>',
            'identifier_data' => '',
          ),
        ),
        'cc_htv_td' => 
        array (
          0 => 
          array (
            'name' => 'Hỗ trợ vốn Nextlend',
            'identifier_account' => '<EMAIL>',
            'identifier_data' => '',
          ),
          1 => 
          array (
            'name' => 'Thẩm định Nextlend',
            'identifier_account' => '<EMAIL>',
            'identifier_data' => '',
          ),
          2 => 
          array (
            'name' => 'Thẩm định 1 Nextlend',
            'identifier_account' => '<EMAIL>',
            'identifier_data' => '',
          ),
        ),
      ),
    ),
    'tele' => 
    array (
      'ACCOUTING_TOPIC' => 1212,
      'PARNER_TOPIC' => NULL,
      'TRICH_TAY_TOPIC' => NULL,
      'EMAIL_REMIND_TOPIC' => NULL,
      'CREATE_REQUEST_TOPIC' => NULL,
      'SEND_REQUEST_TO_PARTNER_TOPIC' => NULL,
      'GHI_SO_TOPIC' => NULL,
      'CUT_OFF_TOPIC' => NULL,
      'CANCEL_MPOS_TOPIC' => NULL,
      'DAY_CHI_DAN_TOPIC' => NULL,
      'QUA_KHU_TOPIC' => NULL,
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'cloud' => 's3',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\storage\\app',
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\storage\\app/public',
        'url' => 'http://nextpay-web.local.com/api-request-debt/public/storage',
        'visibility' => 'public',
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => '',
        'url' => NULL,
        'endpoint' => NULL,
      ),
    ),
    'links' => 
    array (
      'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\public\\storage' => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\storage\\app/public',
    ),
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => 10,
    ),
    'argon' => 
    array (
      'memory' => 1024,
      'threads' => 2,
      'time' => 2,
    ),
  ),
  'logging' => 
  array (
    'default' => 'single',
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\storage\\logs/laravel.log',
        'level' => 'debug',
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'critical',
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'formatter' => NULL,
        'with' => 
        array (
          'stream' => 'php://stderr',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\storage\\logs/laravel.log',
      ),
      'nextlend_api_request_debt' => 
      array (
        'driver' => 'daily',
        'path' => '/NextLendLog/nextlend_api_request_debt.log',
        'level' => 'debug',
        'days' => 0,
        'permission' => 511,
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'smtp',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'host' => 'smtp.gmail.com',
        'port' => '587',
        'encryption' => 'tls',
        'username' => '<EMAIL>',
        'password' => 'bpwefwfljpolacun',
        'timeout' => NULL,
        'auth_mode' => NULL,
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'mailgun' => 
      array (
        'transport' => 'mailgun',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Example',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'nextlend' => 
  array (
    'NEXTLEND_SERVICE_REQUEST_URL' => 'https://dev-service.nextlend.vn/nextlend-v1-service-v2/request.php',
    'NEXTLEND_SERVICE_SECRET_KEY' => 'hyuy43H4fY3453sj',
    'NEXTLEND_SERVICE_ENCRYPT_KEY' => 's00N4fJqAfG7FhUnBp9IKJ5E0okkI',
    'NEXTLEND_SERVICE_VERSION' => '1.0',
    'NEXTLEND_SERVICE_CHANNEL_CODE' => 'NEXTLENDV4',
    'NEXTLEND_CORE_API_URL' => 'https://dev-api.nextlend.vn/api.php',
    'NEXTLEND_CORE_API_APP_SECRET_KEY' => '1234567890',
    'NEXTLEND_CORE_API_APP_ID' => 'xmoney',
    'NEXTLEND_CORE_API_KET_ENCRYPTION_DATA' => '1234567890',
    'THOI_GIAN_CHECK_LAI_YEU_CAU_TRICH' => 30,
    'THOI_GIAN_XU_LY_LENH_LOI_RO_RANG' => 20,
  ),
  'queue' => 
  array (
    'default' => 'redis',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'your-queue-name',
        'suffix' => NULL,
        'region' => 'us-east-1',
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
      ),
    ),
    'failed' => 
    array (
      'driver' => 'database',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'services' => 
  array (
    'mailgun' => 
    array (
      'domain' => NULL,
      'secret' => NULL,
      'endpoint' => 'api.mailgun.net',
    ),
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
    'discord' => 
    array (
      'tao_yc_trich_no' => 'https://discordapp.com/api/webhooks/1375655014416252959/umjmG_SfIj1MqKWMi00GU6aNxHou56J3ugOt8XMSs7YtueLH8rcESrTbCJzFBdG7Ef9v',
      'mails' => 'https://discordapp.com/api/webhooks/1375657417207058472/jsfMF7oChoKONC0zsokPmF6oeR8-GnhmpkWm4fraYy4tOu5odv-1Dwpy-fFJVze8jAoa',
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => '120',
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'requestdebt_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\resources\\views',
    ),
    'compiled' => 'C:\\laragon8\\www\\nextpay-web\\api-request-debt\\storage\\framework\\views',
  ),
  'flare' => 
  array (
    'key' => NULL,
    'reporting' => 
    array (
      'anonymize_ips' => true,
      'collect_git_information' => false,
      'report_queries' => true,
      'maximum_number_of_collected_queries' => 200,
      'report_query_bindings' => true,
      'report_view_data' => true,
      'grouping_type' => NULL,
      'report_logs' => true,
      'maximum_number_of_collected_logs' => 200,
      'censor_request_body_fields' => 
      array (
        0 => 'password',
      ),
    ),
    'send_logs_as_events' => true,
    'censor_request_body_fields' => 
    array (
      0 => 'password',
    ),
  ),
  'ignition' => 
  array (
    'editor' => 'phpstorm',
    'theme' => 'light',
    'enable_share_button' => true,
    'register_commands' => false,
    'ignored_solution_providers' => 
    array (
      0 => 'Facade\\Ignition\\SolutionProviders\\MissingPackageSolutionProvider',
    ),
    'enable_runnable_solutions' => NULL,
    'remote_sites_path' => '',
    'local_sites_path' => '',
    'housekeeping_endpoint_prefix' => '_ignition',
  ),
  'collect_debt_gateway_config' => 
  array (
    'mpos_gateway_response_success' => '00',
    'mpos_gateway_response_waitting' => '90001',
    'mpos_gateway_reponse_name' => 
    array (
      '00' => 'SUCCESS',
      '01' => 'Input error',
      '02' => 'Channel error',
      '03' => 'IP invalid',
      '04' => 'Channel not exit or function error',
      '05' => 'Checksum invalid',
      '06' => 'EncData error',
      '07' => 'Config partner not exits',
      99 => 'Error',
      -46001 => 'Record had exsit',
      90001 => 'Waitting for response partner',
      -46005 => 'Debt is not exist',
      -1 => 'Debt is not exist',
    ),
    'payment_channel' => 
    array (
      'mpos' => 'MPOS',
    ),
  ),
  'collect_debt_email_remind_config' => 
  array (
    'debt_contract_event_status' => 
    array (
      'new' => 1,
      'creating_content' => 2,
      'created_content' => 3,
      'sending_request' => 4,
      'created_request' => 5,
      'failed' => 6,
      'canceled' => 7,
    ),
    'mail' => 
    array (
      'NHAC_NO_THANH_TOAN' => 
      array (
        'sender' => 
        array (
          'name' => 'NEXTLEND',
          'email' => '<EMAIL>',
          'email_reply' => '<EMAIL>',
        ),
        'cc_htv' => 
        array (
          0 => 
          array (
            'name' => 'Hỗ trợ vốn Nextlend',
            'identifier_account' => '<EMAIL>',
            'identifier_data' => '',
          ),
        ),
        'cc_htv_td' => 
        array (
          0 => 
          array (
            'name' => 'Thẩm định Nextlend',
            'identifier_account' => '<EMAIL>',
            'identifier_data' => '',
          ),
        ),
        'cc_all' => 
        array (
          0 => 
          array (
            'name' => 'Hỗ trợ vốn Nextlend',
            'identifier_account' => '<EMAIL>',
            'identifier_data' => '',
          ),
          1 => 
          array (
            'name' => 'Thẩm định Nextlend',
            'identifier_account' => '<EMAIL>',
            'identifier_data' => '',
          ),
        ),
      ),
      'code_sender' => 
      array (
        0 => 'NV_THAM_DINH',
        1 => 'NV_THU_HOI_ONLINE',
      ),
    ),
  ),
  'trustedproxy' => 
  array (
    'proxies' => NULL,
    'headers' => 94,
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
