[2025-08-15 08:45:37] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150845_BUslE3"}} 
[2025-08-15 08:45:37] local.ERROR: Argument 1 passed to <PERSON>lum<PERSON>\Http\Client\PendingRequest::get() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\app\Lib\NextlendCore.php on line 82 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to Illuminate\\Http\\Client\\PendingRequest::get() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Lib\\NextlendCore.php on line 82 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:443)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Lib\\NextlendCore.php(82): Illuminate\\Http\\Client\\PendingRequest->get(NULL, Array)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\Task\\GetThongTinNguoiNhanMailTask.php(30): App\\Lib\\NextlendCore->callRequest(Array, 'ContractV4_getE...', 'get')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\DebtRecoveryContractEventCreateSendAction.php(130): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\Task\\GetThongTinNguoiNhanMailTask->run('MPOS-2505060944...')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction.php(87): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\DebtRecoveryContractEventCreateSendAction->__runMail(Object(App\\Modules\\EmailRemind\\Model\\CollectDebtContractEvent))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\event.php(12): App\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction->HandleEvent('28295')
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}('28295')
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-08-15 08:45:38] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150845_TZIHNb"}} 
[2025-08-15 08:45:39] local.ERROR: Argument 1 passed to Illuminate\Http\Client\PendingRequest::get() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\app\Lib\NextlendCore.php on line 82 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to Illuminate\\Http\\Client\\PendingRequest::get() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Lib\\NextlendCore.php on line 82 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:443)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Lib\\NextlendCore.php(82): Illuminate\\Http\\Client\\PendingRequest->get(NULL, Array)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\Task\\GetThongTinNguoiNhanMailTask.php(30): App\\Lib\\NextlendCore->callRequest(Array, 'ContractV4_getE...', 'get')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\DebtRecoveryContractEventCreateSendAction.php(130): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\Task\\GetThongTinNguoiNhanMailTask->run('MPOS-2505060944...')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction.php(87): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\DebtRecoveryContractEventCreateSendAction->__runMail(Object(App\\Modules\\EmailRemind\\Model\\CollectDebtContractEvent))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\event.php(12): App\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction->HandleEvent('28295')
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}('28295')
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-08-15 08:46:28] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150846_UdmdU9"}} 
[2025-08-15 08:46:28] local.ERROR: Argument 1 passed to Illuminate\Http\Client\PendingRequest::get() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\app\Lib\NextlendCore.php on line 82 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to Illuminate\\Http\\Client\\PendingRequest::get() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Lib\\NextlendCore.php on line 82 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:443)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Lib\\NextlendCore.php(82): Illuminate\\Http\\Client\\PendingRequest->get(NULL, Array)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\Task\\GetThongTinNguoiNhanMailTask.php(30): App\\Lib\\NextlendCore->callRequest(Array, 'ContractV4_getE...', 'get')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\DebtRecoveryContractEventCreateSendAction.php(130): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\Task\\GetThongTinNguoiNhanMailTask->run('MPOS-2505060944...')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction.php(87): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventCreateSendAction\\DebtRecoveryContractEventCreateSendAction->__runMail(Object(App\\Modules\\EmailRemind\\Model\\CollectDebtContractEvent))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\event.php(12): App\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction->HandleEvent('28295')
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}('28295')
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-08-15 08:48:14] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150848_q9fE9L"}} 
[2025-08-15 08:48:54] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150848_3asMra"}} 
[2025-08-15 08:49:20] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150849_vOa0Up"}} 
[2025-08-15 08:50:52] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150850_81mqdT"}} 
[2025-08-15 08:51:05] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150851_bXzDde"}} 
[2025-08-15 08:51:17] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150851_vtP0db"}} 
[2025-08-15 08:51:57] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150851_1ALgj3"}} 
[2025-08-15 08:52:44] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150852_ihnM0B"}} 
[2025-08-15 09:08:00] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150908_08zrKM"}} 
[2025-08-15 09:08:00] local.ERROR: Cannot use 'static' as constant modifier {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot use 'static' as constant modifier at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\SubAction\\GetTemplateMailSubAction.php:13)
[stacktrace]
#0 {main}
"} 
[2025-08-15 09:08:13] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150908_GbdWub"}} 
[2025-08-15 09:08:55] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150908_xz1hzu"}} 
[2025-08-15 09:11:29] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150911_gtE9KZ"}} 
[2025-08-15 09:11:29] local.ERROR: Call to a member function values() on null {"exception":"[object] (Error(code: 0): Call to a member function values() on null at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\SubAction\\GetTemplateMailSubAction.php:30)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\SubAction\\EmailRemindOverdueC3SubAction.php(23): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\SubAction\\GetTemplateMailSubAction->run(Array)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\DebtRecoveryContractEventBuildContentImproveAction.php(170): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\SubAction\\EmailRemindOverdueC3SubAction->run(Object(App\\Modules\\EmailRemind\\Model\\CollectDebtContractEvent))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction.php(79): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\DebtRecoveryContractEventBuildContentImproveAction->__buildMailContent(Object(App\\Modules\\EmailRemind\\Model\\CollectDebtContractEvent))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\event.php(12): App\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction->HandleEvent('28295')
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}('28295')
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#31 {main}
"} 
[2025-08-15 09:11:49] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150911_A9ExVZ"}} 
[2025-08-15 09:11:59] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150911_oixk4l"}} 
[2025-08-15 09:12:15] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150912_2riXHx"}} 
[2025-08-15 09:13:03] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150913_elOFTR"}} 
[2025-08-15 09:13:59] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150913_o6tVjG"}} 
[2025-08-15 09:16:04] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150916_p0bNW0"}} 
[2025-08-15 09:16:05] local.ERROR: syntax error, unexpected '$payload' (T_VARIABLE) {"exception":"[object] (ParseError(code: 0): syntax error, unexpected '$payload' (T_VARIABLE) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\SubAction\\GetTemplateMailSubAction.php:38)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon8\\\\www...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Modules\\\\Ema...')
#2 [internal function]: spl_autoload_call('App\\\\Modules\\\\Ema...')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(809): ReflectionClass->__construct('App\\\\Modules\\\\Ema...')
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(691): Illuminate\\Container\\Container->build('App\\\\Modules\\\\Ema...')
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(796): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Ema...', Array, true)
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(637): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Ema...', Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(781): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Ema...', Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(119): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Ema...', Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\SubAction\\EmailRemindOverdueC3SubAction.php(23): app('App\\\\Modules\\\\Ema...')
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\DebtRecoveryContractEventBuildContentImproveAction.php(170): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\SubAction\\EmailRemindOverdueC3SubAction->run(Object(App\\Modules\\EmailRemind\\Model\\CollectDebtContractEvent))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction.php(79): App\\Modules\\EmailRemind\\Actions\\CollectDebtContractEvent\\DebtRecoveryContractEventBuildContentAction\\DebtRecoveryContractEventBuildContentImproveAction->__buildMailContent(Object(App\\Modules\\EmailRemind\\Model\\CollectDebtContractEvent))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\event.php(12): App\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\PushEventBatchAction->HandleEvent('28295')
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}('28295')
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-08-15 09:16:42] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150916_LMONIJ"}} 
[2025-08-15 09:16:58] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150916_0fbrI8"}} 
[2025-08-15 09:17:26] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150917_JJHyPw"}} 
[2025-08-15 09:17:59] local.INFO: HandleEvent/28295 {"request":{"api_request_id":"DEBT_08150917_aHuwWh"}} 
[2025-08-15 09:19:10] local.INFO: PushEventBatchAction {"request":{"api_request_id":"DEBT_08150919_lOckYu"}} 
[2025-08-15 09:19:11] local.INFO: HandleEvent/28296 {"request":{"api_request_id":"DEBT_08150919_U6W1JP"}} 
[2025-08-15 09:19:11] local.INFO: HandleEvent/28297 {"request":{"api_request_id":"DEBT_08150919_W206gY"}} 
[2025-08-15 09:19:11] local.INFO: HandleEvent/28298 {"request":{"api_request_id":"DEBT_08150919_Wjs40p"}} 
[2025-08-15 09:19:11] local.INFO: HandleEvent/28299 {"request":{"api_request_id":"DEBT_08150919_hWmR6e"}} 
[2025-08-15 09:19:11] local.INFO: HandleEvent/28300 {"request":{"api_request_id":"DEBT_08150919_kT0IE8"}} 
[2025-08-15 09:19:12] local.INFO: HandleEvent/28301 {"request":{"api_request_id":"DEBT_08150919_yroxZv"}} 
[2025-08-15 09:19:12] local.INFO: HandleEvent/28302 {"request":{"api_request_id":"DEBT_08150919_PrUqPE"}} 
[2025-08-15 09:19:13] local.INFO: HandleEvent/28303 {"request":{"api_request_id":"DEBT_08150919_Tcw1pw"}} 
[2025-08-15 09:19:13] local.INFO: HandleEvent/28305 {"request":{"api_request_id":"DEBT_08150919_rhbOSF"}} 
[2025-08-15 09:19:13] local.INFO: HandleEvent/28304 {"request":{"api_request_id":"DEBT_08150919_kwykY6"}} 
[2025-08-15 09:19:14] local.INFO: HandleEvent/28306 {"request":{"api_request_id":"DEBT_08150919_T0MiAf"}} 
[2025-08-15 09:19:14] local.INFO: HandleEvent/28307 {"request":{"api_request_id":"DEBT_08150919_3KS3rZ"}} 
[2025-08-15 09:19:14] local.INFO: HandleEvent/28308 {"request":{"api_request_id":"DEBT_08150919_YTmkTg"}} 
[2025-08-15 09:19:14] local.INFO: HandleEvent/28309 {"request":{"api_request_id":"DEBT_08150919_m7cPfq"}} 
[2025-08-15 09:19:14] local.INFO: HandleEvent/28310 {"request":{"api_request_id":"DEBT_08150919_NPlhth"}} 
[2025-08-15 09:19:15] local.INFO: HandleEvent/28311 {"request":{"api_request_id":"DEBT_08150919_kPM3B7"}} 
[2025-08-15 09:19:15] local.INFO: HandleEvent/28312 {"request":{"api_request_id":"DEBT_08150919_a3LzCp"}} 
[2025-08-15 09:19:15] local.INFO: HandleEvent/28313 {"request":{"api_request_id":"DEBT_08150919_KV60T0"}} 
[2025-08-15 09:19:16] local.INFO: HandleEvent/28314 {"request":{"api_request_id":"DEBT_08150919_oHlztG"}} 
[2025-08-15 09:19:16] local.INFO: HandleEvent/28315 {"request":{"api_request_id":"DEBT_08150919_HJydcs"}} 
[2025-08-15 09:19:17] local.INFO: HandleEvent/28316 {"request":{"api_request_id":"DEBT_08150919_UXMsD4"}} 
[2025-08-15 09:19:17] local.INFO: HandleEvent/28317 {"request":{"api_request_id":"DEBT_08150919_WEnUiW"}} 
[2025-08-15 09:19:17] local.INFO: HandleEvent/28318 {"request":{"api_request_id":"DEBT_08150919_bbDhYZ"}} 
[2025-08-15 09:19:17] local.INFO: HandleEvent/28319 {"request":{"api_request_id":"DEBT_08150919_5DhK15"}} 
[2025-08-15 09:19:17] local.INFO: HandleEvent/28320 {"request":{"api_request_id":"DEBT_08150919_i2Xs3f"}} 
[2025-08-15 09:19:18] local.INFO: HandleEvent/28321 {"request":{"api_request_id":"DEBT_08150919_LgQQRL"}} 
[2025-08-15 09:19:18] local.INFO: HandleEvent/28322 {"request":{"api_request_id":"DEBT_08150919_rjHP8X"}} 
[2025-08-15 09:19:18] local.INFO: HandleEvent/28323 {"request":{"api_request_id":"DEBT_08150919_cKTb0r"}} 
[2025-08-15 09:19:19] local.INFO: HandleEvent/28325 {"request":{"api_request_id":"DEBT_08150919_HsJT5Z"}} 
[2025-08-15 09:19:19] local.INFO: HandleEvent/28324 {"request":{"api_request_id":"DEBT_08150919_nBwv5v"}} 
[2025-08-15 09:27:43] local.INFO: DebtRecoveryHandleFirstUpcomingPlan {"request":{"api_request_id":"DEBT_08150927_JLCFNb"}} 
[2025-08-15 09:29:21] local.INFO: DebtRecoveryHandleSapToiHan {"request":{"api_request_id":"DEBT_08150929_9oE4ub"}} 
[2025-08-15 10:19:35] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151019_3UCUTv"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1677 {"request":{"api_request_id":"DEBT_08151019_YZuuCu"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1678 {"request":{"api_request_id":"DEBT_08151019_xNajbT"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1680 {"request":{"api_request_id":"DEBT_08151019_j0gEXO"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1681 {"request":{"api_request_id":"DEBT_08151019_LyaaRZ"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1679 {"request":{"api_request_id":"DEBT_08151019_yPURSO"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1682 {"request":{"api_request_id":"DEBT_08151019_gzxHGJ"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1683 {"request":{"api_request_id":"DEBT_08151019_KQUQXe"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1684 {"request":{"api_request_id":"DEBT_08151019_NMS9le"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1687 {"request":{"api_request_id":"DEBT_08151019_2Bgvse"}} 
[2025-08-15 10:19:36] local.INFO: HandleOverCycleOverdue/1685 {"request":{"api_request_id":"DEBT_08151019_lNG7Rf"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1689 {"request":{"api_request_id":"DEBT_08151019_RhxVje"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1690 {"request":{"api_request_id":"DEBT_08151019_P7S9qX"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1694 {"request":{"api_request_id":"DEBT_08151019_Zjordc"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1693 {"request":{"api_request_id":"DEBT_08151019_1uAs3k"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1696 {"request":{"api_request_id":"DEBT_08151019_mPztwJ"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1697 {"request":{"api_request_id":"DEBT_08151019_RcPBLv"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151019_ERz1uW"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1701 {"request":{"api_request_id":"DEBT_08151019_7RrwfM"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1705 {"request":{"api_request_id":"DEBT_08151019_wNzZLS"}} 
[2025-08-15 10:19:37] local.INFO: HandleOverCycleOverdue/1711 {"request":{"api_request_id":"DEBT_08151019_vWl5QL"}} 
[2025-08-15 10:19:37] local.INFO: [CreateEventChamKyQuaHan --->1707] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1707` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:37] local.INFO: [CreateEventChamKyQuaHan --->1711] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1711` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1713 {"request":{"api_request_id":"DEBT_08151019_07JWuJ"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1715 {"request":{"api_request_id":"DEBT_08151019_KxScEn"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1717 {"request":{"api_request_id":"DEBT_08151019_jx5QJ5"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1718 {"request":{"api_request_id":"DEBT_08151019_vpDb7B"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1719 {"request":{"api_request_id":"DEBT_08151019_JUrg33"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1720 {"request":{"api_request_id":"DEBT_08151019_kKLY9d"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1721 {"request":{"api_request_id":"DEBT_08151019_OU2886"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1722 {"request":{"api_request_id":"DEBT_08151019_IvFxit"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1724 {"request":{"api_request_id":"DEBT_08151019_a7rplv"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1727 {"request":{"api_request_id":"DEBT_08151019_BGVFM4"}} 
[2025-08-15 10:19:38] local.INFO: [CreateEventChamKyQuaHan --->1722] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1722` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1736 {"request":{"api_request_id":"DEBT_08151019_4AFKrS"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1735 {"request":{"api_request_id":"DEBT_08151019_zzCRwr"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1734 {"request":{"api_request_id":"DEBT_08151019_H95Xbd"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1747 {"request":{"api_request_id":"DEBT_08151019_Jeikl8"}} 
[2025-08-15 10:19:38] local.INFO: HandleOverCycleOverdue/1748 {"request":{"api_request_id":"DEBT_08151019_kDHJne"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1749 {"request":{"api_request_id":"DEBT_08151019_DfWc1U"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1800 {"request":{"api_request_id":"DEBT_08151019_tMu3E0"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1798 {"request":{"api_request_id":"DEBT_08151019_npgthe"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1751 {"request":{"api_request_id":"DEBT_08151019_UnnX4z"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1799 {"request":{"api_request_id":"DEBT_08151019_e3UbtT"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1802 {"request":{"api_request_id":"DEBT_08151019_6eQ8bX"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1806 {"request":{"api_request_id":"DEBT_08151019_99bffZ"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1812 {"request":{"api_request_id":"DEBT_08151019_r3lw8g"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1810 {"request":{"api_request_id":"DEBT_08151019_79oCuF"}} 
[2025-08-15 10:19:39] local.INFO: HandleOverCycleOverdue/1824 {"request":{"api_request_id":"DEBT_08151019_u99i59"}} 
[2025-08-15 10:19:39] local.INFO: [CreateEventChamKyQuaHan --->1810] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1810` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1827 {"request":{"api_request_id":"DEBT_08151019_dxCn6G"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1830 {"request":{"api_request_id":"DEBT_08151019_sUlcqe"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1828 {"request":{"api_request_id":"DEBT_08151019_4GbVOa"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1832 {"request":{"api_request_id":"DEBT_08151019_6pd8Yo"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1833 {"request":{"api_request_id":"DEBT_08151019_Ge0Zga"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1841 {"request":{"api_request_id":"DEBT_08151019_1LOO4u"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1843 {"request":{"api_request_id":"DEBT_08151019_p7EEWG"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1845 {"request":{"api_request_id":"DEBT_08151019_MJFM6N"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1846 {"request":{"api_request_id":"DEBT_08151019_uJgvjt"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1847 {"request":{"api_request_id":"DEBT_08151019_RF6Fy3"}} 
[2025-08-15 10:19:40] local.INFO: [CreateEventChamKyQuaHan --->1841] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1841` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1848 {"request":{"api_request_id":"DEBT_08151019_WQTtFW"}} 
[2025-08-15 10:19:40] local.INFO: [CreateEventChamKyQuaHan --->1843] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1843` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:40] local.INFO: [CreateEventChamKyQuaHan --->1845] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1845` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:40] local.INFO: [CreateEventChamKyQuaHan --->1846] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1846` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:40] local.INFO: [CreateEventChamKyQuaHan --->1847] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1847` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1849 {"request":{"api_request_id":"DEBT_08151019_13yLlW"}} 
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1850 {"request":{"api_request_id":"DEBT_08151019_pzm4o3"}} 
[2025-08-15 10:19:40] local.INFO: [CreateEventChamKyQuaHan --->1848] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1848` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:40] local.INFO: HandleOverCycleOverdue/1852 {"request":{"api_request_id":"DEBT_08151019_qtXlj8"}} 
[2025-08-15 10:19:41] local.INFO: [CreateEventChamKyQuaHan --->1849] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1849` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:41] local.INFO: [CreateEventChamKyQuaHan --->1851] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1851` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:41] local.INFO: [CreateEventChamKyQuaHan --->1850] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1850` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1864 {"request":{"api_request_id":"DEBT_08151019_BHf3jd"}} 
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1866 {"request":{"api_request_id":"DEBT_08151019_X5Hn0D"}} 
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1867 {"request":{"api_request_id":"DEBT_08151019_r8oKpW"}} 
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1870 {"request":{"api_request_id":"DEBT_08151019_RbTljE"}} 
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1868 {"request":{"api_request_id":"DEBT_08151019_K3QQNv"}} 
[2025-08-15 10:19:41] local.INFO: [CreateEventChamKyQuaHan --->1864] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1864` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:41] local.INFO: [CreateEventChamKyQuaHan --->1870] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1870` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:41] local.INFO: [CreateEventChamKyQuaHan --->1868] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1868` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1871 {"request":{"api_request_id":"DEBT_08151019_aVBOZu"}} 
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1872 {"request":{"api_request_id":"DEBT_08151019_o3IN0y"}} 
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1884 {"request":{"api_request_id":"DEBT_08151019_eMjhww"}} 
[2025-08-15 10:19:41] local.INFO: [CreateEventChamKyQuaHan --->1871] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1871` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1886 {"request":{"api_request_id":"DEBT_08151019_V2N8iw"}} 
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1885 {"request":{"api_request_id":"DEBT_08151019_z91sQD"}} 
[2025-08-15 10:19:41] local.INFO: HandleOverCycleOverdue/1888 {"request":{"api_request_id":"DEBT_08151019_IlwKws"}} 
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1889 {"request":{"api_request_id":"DEBT_08151019_4jXgdY"}} 
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1890 {"request":{"api_request_id":"DEBT_08151019_ztyf7N"}} 
[2025-08-15 10:19:42] local.INFO: [CreateEventChamKyQuaHan --->1888] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1888` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:42] local.INFO: [CreateEventChamKyQuaHan --->1885] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1885` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:42] local.INFO: [CreateEventChamKyQuaHan --->1886] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1886` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1895 {"request":{"api_request_id":"DEBT_08151019_Ajc4lJ"}} 
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1894 {"request":{"api_request_id":"DEBT_08151019_P9yFF0"}} 
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1898 {"request":{"api_request_id":"DEBT_08151019_yVBCrk"}} 
[2025-08-15 10:19:42] local.INFO: [CreateEventChamKyQuaHan --->1890] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1890` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1899 {"request":{"api_request_id":"DEBT_08151019_ZsZHiQ"}} 
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1901 {"request":{"api_request_id":"DEBT_08151019_h0aqAX"}} 
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1905 {"request":{"api_request_id":"DEBT_08151019_NN9z8i"}} 
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1903 {"request":{"api_request_id":"DEBT_08151019_yADyBu"}} 
[2025-08-15 10:19:42] local.INFO: HandleOverCycleOverdue/1902 {"request":{"api_request_id":"DEBT_08151019_RCOE3A"}} 
[2025-08-15 10:19:43] local.INFO: [CreateEventChamKyQuaHan --->1905] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1905` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:43] local.INFO: HandleOverCycleOverdue/1906 {"request":{"api_request_id":"DEBT_08151019_kdytXT"}} 
[2025-08-15 10:19:43] local.INFO: HandleOverCycleOverdue/1912 {"request":{"api_request_id":"DEBT_08151019_tVRr8w"}} 
[2025-08-15 10:19:43] local.INFO: HandleOverCycleOverdue/1916 {"request":{"api_request_id":"DEBT_08151019_UhNR44"}} 
[2025-08-15 10:19:43] local.INFO: HandleOverCycleOverdue/1917 {"request":{"api_request_id":"DEBT_08151019_CWIjFN"}} 
[2025-08-15 10:19:43] local.INFO: HandleOverCycleOverdue/1930 {"request":{"api_request_id":"DEBT_08151019_PObg48"}} 
[2025-08-15 10:19:43] local.INFO: HandleOverCycleOverdue/1931 {"request":{"api_request_id":"DEBT_08151019_SVGHOy"}} 
[2025-08-15 10:19:43] local.INFO: HandleOverCycleOverdue/1932 {"request":{"api_request_id":"DEBT_08151019_6WXUkO"}} 
[2025-08-15 10:19:43] local.INFO: HandleOverCycleOverdue/1933 {"request":{"api_request_id":"DEBT_08151019_Py3wqY"}} 
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1949 {"request":{"api_request_id":"DEBT_08151019_ZAH12d"}} 
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1939 {"request":{"api_request_id":"DEBT_08151019_s3G3O2"}} 
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1950 {"request":{"api_request_id":"DEBT_08151019_WjjOt9"}} 
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1934 {"request":{"api_request_id":"DEBT_08151019_2cBMO1"}} 
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1945 {"request":{"api_request_id":"DEBT_08151019_QVruEW"}} 
[2025-08-15 10:19:44] local.INFO: [CreateEventChamKyQuaHan --->1949] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1949` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1951 {"request":{"api_request_id":"DEBT_08151019_QTs5at"}} 
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1952 {"request":{"api_request_id":"DEBT_08151019_ufh0je"}} 
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1960 {"request":{"api_request_id":"DEBT_08151019_zuJAvr"}} 
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1961 {"request":{"api_request_id":"DEBT_08151019_eGSt2D"}} 
[2025-08-15 10:19:44] local.INFO: HandleOverCycleOverdue/1953 {"request":{"api_request_id":"DEBT_08151019_o4pgmy"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1962 {"request":{"api_request_id":"DEBT_08151019_tjuvHV"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1964 {"request":{"api_request_id":"DEBT_08151019_cqmjyW"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1966 {"request":{"api_request_id":"DEBT_08151019_sZ2vCG"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1967 {"request":{"api_request_id":"DEBT_08151019_3Te2Ci"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1965 {"request":{"api_request_id":"DEBT_08151019_qSSRFw"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1968 {"request":{"api_request_id":"DEBT_08151019_H0LDaJ"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1969 {"request":{"api_request_id":"DEBT_08151019_dNXpcK"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1971 {"request":{"api_request_id":"DEBT_08151019_GIDvUb"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1970 {"request":{"api_request_id":"DEBT_08151019_TsZJ1Y"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1972 {"request":{"api_request_id":"DEBT_08151019_CzbF4u"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1973 {"request":{"api_request_id":"DEBT_08151019_bOKP13"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1976 {"request":{"api_request_id":"DEBT_08151019_GbacPK"}} 
[2025-08-15 10:19:45] local.INFO: HandleOverCycleOverdue/1977 {"request":{"api_request_id":"DEBT_08151019_cWTCTF"}} 
[2025-08-15 10:19:46] local.INFO: HandleOverCycleOverdue/1978 {"request":{"api_request_id":"DEBT_08151019_fWkSCD"}} 
[2025-08-15 10:19:46] local.INFO: HandleOverCycleOverdue/1979 {"request":{"api_request_id":"DEBT_08151019_6I8tKe"}} 
[2025-08-15 10:19:46] local.INFO: HandleOverCycleOverdue/1980 {"request":{"api_request_id":"DEBT_08151019_VLLlhl"}} 
[2025-08-15 10:19:46] local.INFO: HandleOverCycleOverdue/1981 {"request":{"api_request_id":"DEBT_08151019_B8QNgQ"}} 
[2025-08-15 10:19:46] local.INFO: HandleOverCycleOverdue/1982 {"request":{"api_request_id":"DEBT_08151019_bcXwmh"}} 
[2025-08-15 10:19:46] local.INFO: HandleOverCycleOverdue/1983 {"request":{"api_request_id":"DEBT_08151019_kUCz38"}} 
[2025-08-15 10:19:46] local.INFO: HandleOverCycleOverdue/1984 {"request":{"api_request_id":"DEBT_08151019_CFrC5Q"}} 
[2025-08-15 10:19:47] local.INFO: HandleOverCycleOverdue/1986 {"request":{"api_request_id":"DEBT_08151019_XPaOuS"}} 
[2025-08-15 10:19:47] local.INFO: HandleOverCycleOverdue/1989 {"request":{"api_request_id":"DEBT_08151019_o3dW7D"}} 
[2025-08-15 10:19:47] local.INFO: HandleOverCycleOverdue/1988 {"request":{"api_request_id":"DEBT_08151019_TDYnUF"}} 
[2025-08-15 10:19:47] local.INFO: HandleOverCycleOverdue/1987 {"request":{"api_request_id":"DEBT_08151019_TgfvlQ"}} 
[2025-08-15 10:19:47] local.INFO: HandleOverCycleOverdue/1991 {"request":{"api_request_id":"DEBT_08151019_4ZuI2f"}} 
[2025-08-15 10:19:47] local.INFO: HandleOverCycleOverdue/1992 {"request":{"api_request_id":"DEBT_08151019_7GXjU3"}} 
[2025-08-15 10:19:47] local.INFO: HandleOverCycleOverdue/1993 {"request":{"api_request_id":"DEBT_08151019_7etoGi"}} 
[2025-08-15 10:19:47] local.INFO: HandleOverCycleOverdue/1994 {"request":{"api_request_id":"DEBT_08151019_wQs5HX"}} 
[2025-08-15 10:19:47] local.INFO: HandleOverCycleOverdue/2012 {"request":{"api_request_id":"DEBT_08151019_Vna2ki"}} 
[2025-08-15 10:19:48] local.INFO: HandleOverCycleOverdue/2018 {"request":{"api_request_id":"DEBT_08151019_VyjidA"}} 
[2025-08-15 10:19:48] local.INFO: HandleOverCycleOverdue/2017 {"request":{"api_request_id":"DEBT_08151019_dBeOnx"}} 
[2025-08-15 10:20:13] local.INFO: HandleOverCycleOverdue/1677 {"request":{"api_request_id":"DEBT_08151020_UTSj06"}} 
[2025-08-15 10:20:20] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151020_MrDcYT"}} 
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151020_8MGtgx"}} 
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1722 {"request":{"api_request_id":"DEBT_08151020_TWBL7v"}} 
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1711 {"request":{"api_request_id":"DEBT_08151020_H1l8ma"}} 
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1810 {"request":{"api_request_id":"DEBT_08151020_hqCk1y"}} 
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1841 {"request":{"api_request_id":"DEBT_08151020_XYYjKJ"}} 
[2025-08-15 10:20:20] local.INFO: [CreateEventChamKyQuaHan --->1707] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1707` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:20] local.INFO: [CreateEventChamKyQuaHan --->1722] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1722` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:20] local.INFO: [CreateEventChamKyQuaHan --->1711] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1711` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:20] local.INFO: [CreateEventChamKyQuaHan --->1810] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1810` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:20] local.INFO: [CreateEventChamKyQuaHan --->1841] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1841` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1843 {"request":{"api_request_id":"DEBT_08151020_r7DHtP"}} 
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1845 {"request":{"api_request_id":"DEBT_08151020_Wn9c2f"}} 
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1847 {"request":{"api_request_id":"DEBT_08151020_UhR2Oo"}} 
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1846 {"request":{"api_request_id":"DEBT_08151020_n12Rtd"}} 
[2025-08-15 10:20:20] local.INFO: HandleOverCycleOverdue/1848 {"request":{"api_request_id":"DEBT_08151020_Pp2sK0"}} 
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1845] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1845` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1843] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1843` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1847] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1847` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1846] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1846` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1848] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1848` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1850 {"request":{"api_request_id":"DEBT_08151020_yVyHS4"}} 
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1849 {"request":{"api_request_id":"DEBT_08151020_Q2nw8Q"}} 
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1864 {"request":{"api_request_id":"DEBT_08151020_ya3eKY"}} 
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1851 {"request":{"api_request_id":"DEBT_08151020_75CwX1"}} 
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1868 {"request":{"api_request_id":"DEBT_08151020_I1faoM"}} 
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1850] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1850` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1849] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1849` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1864] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1864` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1851] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1851` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1868] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1868` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1870 {"request":{"api_request_id":"DEBT_08151020_ZhrpCO"}} 
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1871 {"request":{"api_request_id":"DEBT_08151020_sjbwBU"}} 
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1885 {"request":{"api_request_id":"DEBT_08151020_lVjh3w"}} 
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1886 {"request":{"api_request_id":"DEBT_08151020_jEwdGl"}} 
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1888 {"request":{"api_request_id":"DEBT_08151020_3uWipK"}} 
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1870] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1870` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1871] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1871` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1885] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1885` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1886] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1886` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1890 {"request":{"api_request_id":"DEBT_08151020_NO8Vgt"}} 
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1888] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1888` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1905 {"request":{"api_request_id":"DEBT_08151020_6BouE9"}} 
[2025-08-15 10:20:21] local.INFO: HandleOverCycleOverdue/1949 {"request":{"api_request_id":"DEBT_08151020_9WUFZ4"}} 
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1890] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1890` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1905] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1905` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:21] local.INFO: [CreateEventChamKyQuaHan --->1949] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/1949` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:20:26] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151020_xpv5Dh"}} 
[2025-08-15 10:21:17] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151021_qaCrCQ"}} 
[2025-08-15 10:22:41] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151022_qwy6qD"}} 
[2025-08-15 10:22:42] local.ERROR: Call to a member function toArray() on bool {"exception":"[object] (Error(code: 0): Call to a member function toArray() on bool at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\CreateOverCycleOverdueEventAction.php:103)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\event.php(23): App\\Modules\\CollectDebt\\Actions\\CollectDebtEvent\\CreateOverCycleOverdueEventAction->HandleOverCycleOverdue('1707')
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}('1707')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#28 {main}
"} 
[2025-08-15 10:25:12] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151025_VAFHwp"}} 
[2025-08-15 10:25:28] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151025_GlMAzE"}} 
[2025-08-15 10:25:29] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151025_opabHg"}} 
[2025-08-15 10:25:51] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151025_gG56g3"}} 
[2025-08-15 10:26:01] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151026_gas1ZH"}} 
[2025-08-15 10:26:09] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151026_Nxne8S"}} 
[2025-08-15 10:27:01] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151027_O5JwK6"}} 
[2025-08-15 10:27:51] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151027_2TFw6c"}} 
[2025-08-15 10:28:09] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151028_XRrM5g"}} 
[2025-08-15 10:28:40] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151028_iuHbWp"}} 
[2025-08-15 10:28:40] local.INFO: HandleOverCycleOverdue/1711 {"request":{"api_request_id":"DEBT_08151028_zcmwkT"}} 
[2025-08-15 10:28:40] local.INFO: HandleOverCycleOverdue/1722 {"request":{"api_request_id":"DEBT_08151028_CwW6Md"}} 
[2025-08-15 10:28:40] local.INFO: HandleOverCycleOverdue/1810 {"request":{"api_request_id":"DEBT_08151028_GiQuxt"}} 
[2025-08-15 10:28:40] local.INFO: HandleOverCycleOverdue/1841 {"request":{"api_request_id":"DEBT_08151028_daxNrf"}} 
[2025-08-15 10:28:40] local.INFO: HandleOverCycleOverdue/1843 {"request":{"api_request_id":"DEBT_08151028_0EGiRy"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1845 {"request":{"api_request_id":"DEBT_08151028_WxkJEg"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1846 {"request":{"api_request_id":"DEBT_08151028_BKG85P"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1847 {"request":{"api_request_id":"DEBT_08151028_NG2NMr"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1848 {"request":{"api_request_id":"DEBT_08151028_uikgoc"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1849 {"request":{"api_request_id":"DEBT_08151028_WhAxNk"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1851 {"request":{"api_request_id":"DEBT_08151028_hLQ1ZG"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1850 {"request":{"api_request_id":"DEBT_08151028_OTVGIF"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1868 {"request":{"api_request_id":"DEBT_08151028_xUshhH"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1864 {"request":{"api_request_id":"DEBT_08151028_4If0JO"}} 
[2025-08-15 10:28:41] local.INFO: HandleOverCycleOverdue/1870 {"request":{"api_request_id":"DEBT_08151028_cbjrfd"}} 
[2025-08-15 10:28:42] local.INFO: HandleOverCycleOverdue/1885 {"request":{"api_request_id":"DEBT_08151028_er3Hz7"}} 
[2025-08-15 10:28:42] local.INFO: HandleOverCycleOverdue/1871 {"request":{"api_request_id":"DEBT_08151028_dV2Vhj"}} 
[2025-08-15 10:28:42] local.INFO: HandleOverCycleOverdue/1888 {"request":{"api_request_id":"DEBT_08151028_bP0vyR"}} 
[2025-08-15 10:28:42] local.INFO: HandleOverCycleOverdue/1890 {"request":{"api_request_id":"DEBT_08151028_pNgp1x"}} 
[2025-08-15 10:28:43] local.INFO: HandleOverCycleOverdue/1905 {"request":{"api_request_id":"DEBT_08151028_M2Dwr3"}} 
[2025-08-15 10:28:43] local.INFO: HandleOverCycleOverdue/1949 {"request":{"api_request_id":"DEBT_08151028_rrfls7"}} 
[2025-08-15 10:28:46] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151028_w217j9"}} 
[2025-08-15 10:29:24] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151029_UbWAV1"}} 
[2025-08-15 10:29:24] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151029_p1J9Mr"}} 
[2025-08-15 10:29:26] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151029_YXZwgj"}} 
[2025-08-15 10:29:27] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151029_861bW7"}} 
[2025-08-15 10:29:50] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151029_4JAN2z"}} 
[2025-08-15 10:29:51] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151029_aDD9Ck"}} 
[2025-08-15 10:29:52] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151029_hhQk8O"}} 
[2025-08-15 10:29:53] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151029_BoVMEX"}} 
[2025-08-15 10:29:53] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151029_7FLoUc"}} 
[2025-08-15 10:32:50] local.INFO: HandleOverCycleOverdue/1707 {"request":{"api_request_id":"DEBT_08151032_8qnpI7"}} 
[2025-08-15 10:35:37] local.INFO: CreateOverCycleOverdueEventAction {"request":{"api_request_id":"DEBT_08151035_otefco"}} 
[2025-08-15 10:35:37] local.INFO: HandleOverCycleOverdue/1744 {"request":{"api_request_id":"DEBT_08151035_hacftE"}} 
[2025-08-15 10:35:37] local.INFO: HandleOverCycleOverdue/2020 {"request":{"api_request_id":"DEBT_08151035_tfuJrS"}} 
[2025-08-15 10:35:38] local.INFO: [CreateEventChamKyQuaHan --->2020] failed: Server error: `POST http://nextpay-web.local.com/api-request-debt/public/HandleOverCycleOverdue/2020` resulted in a `500 Internal Server Error` response:
<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\[ (truncated...)
  
[2025-08-15 10:35:44] local.INFO: HandleOverCycleOverdue/2020 {"request":{"api_request_id":"DEBT_08151035_c4Dyai"}} 
[2025-08-15 10:39:00] local.INFO: HandleOverCycleOverdue/2020 {"request":{"api_request_id":"DEBT_08151039_rdMbmk"}} 
[2025-08-15 10:41:29] local.INFO: HandleOverCycleOverdue/2020 {"request":{"api_request_id":"DEBT_08151041_9YimqV"}} 
[2025-08-15 10:41:33] local.INFO: HandleOverCycleOverdue/2020 {"request":{"api_request_id":"DEBT_08151041_lBm0yf"}} 
