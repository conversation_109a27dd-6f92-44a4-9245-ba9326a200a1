<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;

class CheckRequestPaymentBatchAction
{
	private const CHUNK_LIMIT = 30;
	private const POOL_CONCURRENCY = 5;
	private const HTTP_TIMEOUT = 10;
	private const BATCH_LIMIT_IN_SECONDS = 60;

	private array $__processedIds = [];
	private array $__errorIds = [];


	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();
		$whereRaw = $this->buildWhereCondition();

		CollectDebtRequest::query()
			->whereRaw($whereRaw)
			->select(['*'])
			->chunkById(self::CHUNK_LIMIT, function (Collection $collectDebtRequests) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processCheckBatchRequest($client, $collectDebtRequests);
			});

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}

	private function createHttpClient(): Client
	{
		return new Client([
			'base_uri' => config('app.url'),
			'timeout' => self::HTTP_TIMEOUT,
			'verify' => false
		]);
	}

	private function buildWhereCondition(): string
	{
		$checkIntervalSeconds = config('nextlend.THOI_GIAN_CHECK_LAI_YEU_CAU_TRICH') * 60;

		return sprintf(
			"payment_method_code = 'MPOS' AND (status = %s OR status = %s)
                                    AND status_payment = %s
                                    AND time_receivered IS NULL
                                    AND (time_checked IS NULL OR time_checked + %d < %d)",
			CollectDebtEnum::REQUEST_STT_MOI_TAO,
			CollectDebtEnum::REQUEST_STT_DA_DUYET,
			CollectDebtEnum::REQUEST_STT_PM_DA_GUI,
			$checkIntervalSeconds,
			time()
		);
	}

	private function processCheckBatchRequest(Client $client, Collection $collectDebtRequests): void
	{
		// Generator
		$requests = function () use ($collectDebtRequests) {
			foreach ($collectDebtRequests as $rq) {
				$url = config('app.url') . '/HandleCheckMposRequest/' . $rq->partner_request_id;
				yield $rq->partner_request_id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $partnerRequestId) {
				// $body = (string)$response->getBody();
				Log::info("[CheckRequest --->$partnerRequestId] success");
				$this->__processedIds[] = $partnerRequestId;
			},
			'rejected' => function (\Throwable $reason, string $partnerRequestId) {
				$msg = "[CheckRequest --->$partnerRequestId] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function handleCheckMposRequest(string $partnerRequestId): string
	{
		$request = request();

		$collectDebtRequest = CollectDebtRequest::query()->with('collectDebtPartner')->firstWhere(['partner_request_id' => $partnerRequestId]);

		if (!$collectDebtRequest) {
			return 'Request NotFound';
		}

		if (!$collectDebtRequest->collectDebtPartner) {
			$collectDebtRequestChecked = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request);
			$collectDebtRequest->mpos_debt_result = $collectDebtRequestChecked;
		}

		// có partner rồi thì update timecheck
		if ($collectDebtRequest->collectDebtPartner) {
			$collectDebtRequest->forceFill(['time_checked' => time()])->update();
		}

		return $collectDebtRequest->partner_request_id;
	}
}  // End class