<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction;

use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\DebtRecoveryLedgerAccountingSummaryV2Action;

class AccountingSummaryBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtLedger::query()
			->where('status', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN)
			->where('status_summary', CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT)
			->chunkById(self::CHUNK_LIMIT, function (Collection $listPartner) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processBatch($client, $listPartner);
			});

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}

	
	private function processBatch(Client $client, Collection $listLedger): void
	{
		// Generator
		$requests = function () use ($listLedger) {
			foreach ($listLedger as $collectDebtLedger) {
				$url = config('app.url') . '/HandleAccoutingSummary/' . $collectDebtLedger->id;
				yield $collectDebtLedger->id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, $id) {
				// $body = (string)$response->getBody();
				Log::info("[PartnerCheck --->$id] success");
				$this->__processedIds[] = $id;
			},
			'rejected' => function (\Throwable $reason, string $id) {
				$msg = "[PartnerCheck --->$id] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	// $id --> id sổ
	public function HandleAccoutingSummary($id)
	{
		$collectDebtLedger = CollectDebtLedger::query()
			->where('id', $id)
			->where('status', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN)
			->where('status_summary', CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT)
			->first();

		if (!$collectDebtLedger) {
			return 'Ledger NotFound or invalid status';
		}

		$r = app(DebtRecoveryLedgerAccountingSummaryV2Action::class)->run($collectDebtLedger);
		return $r;
	}
}  // End class