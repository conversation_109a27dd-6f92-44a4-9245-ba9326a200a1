<?php

namespace App\Modules\CollectDebt\Actions;

use GuzzleHttp\Client;

class BatchProcessingAction
{
	public const CHUNK_LIMIT = 30;
	public const POOL_CONCURRENCY = 5;
	public const HTTP_TIMEOUT = 30;
	public const BATCH_LIMIT_IN_SECONDS = 60;

	public array $__processedIds = [];
	public array $__errorIds = [];

	public function createHttpClient(): Client
	{
		return new Client([
			'base_uri' => config('app.url'),
			'timeout' => self::HTTP_TIMEOUT,
			'verify' => false
		]);
	}
} // End class
